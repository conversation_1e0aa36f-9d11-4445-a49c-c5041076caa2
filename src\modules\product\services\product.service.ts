import productsData from '@/config/data/products.json';
import { Product } from '../types';

const products: Product[] = productsData as unknown as Product[];

export const productService = {
	getProducts: (): Product[] => products,

	getProductBySlug: (slug: string): Product | undefined =>
		products.find(product => product.slug === slug),

	getFeaturedProducts: (): Product[] =>
		products.filter(product => product.isFeatured),

	getNewProducts: (): Product[] =>
		products.filter(product => product.isNew),

	getProductsByCategory: (category: string): Product[] =>
		products.filter(product => product.category === category),

	getRelatedProducts: (productId: string, limit = 4): Product[] => {
		const currentProduct = products.find(product => product.id === productId);
		if (!currentProduct) return [];
		return products
			.filter(product =>
				product.id !== productId &&
				(product.category === currentProduct.category ||
					product.tags?.some(tag => currentProduct.tags?.includes(tag)))
			)
			.slice(0, limit);
	}
};
