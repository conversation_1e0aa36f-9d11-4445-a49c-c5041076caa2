export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  originalPrice?: number;
  discount?: number;
  category: string;
  brand: string;
  stock: number;
  rating: number;
  reviewCount: number;
  images: string[];
  specifications?: Record<string, string>;
  features?: string[];
  isFeatured?: boolean;
  isNew?: boolean;
  tags?: string[];
}

export interface ProductCardProps {
  product: Product;
  className?: string;
}

export interface ProductGridProps {
  products: Product[];
  className?: string;
  columns?: 2 | 3 | 4 | 5;
}