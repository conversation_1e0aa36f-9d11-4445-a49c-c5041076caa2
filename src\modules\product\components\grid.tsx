import { cn } from '@/shared/lib/shadcn';
import { ProductGridProps } from '../types';
import { ProductCard } from './card';

export function ProductGrid({
  products,
  className,
  columns = 3 // Mudei default para 3 para look mais premium
}: ProductGridProps) {
  // Grid mais premium com menos produtos por linha
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3', // 3 colunas máximo em desktop
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4', // Só 4 em telas muito grandes
    5: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
  };

  return (
    <div className="relative">
      {/* Grid container com espaçamento premium */}
      <div className={cn(
        // Espaçamento muito mais generoso
        'grid gap-8 md:gap-10 lg:gap-12',
        gridCols[columns],
        // Padding lateral para respirar
        'px-4 md:px-0',
        className
      )}>
        {products.map((product, index) => (
          <div
            key={product.id}
            className="animate-in fade-in slide-in-from-bottom-4"
            style={{
              animationDelay: `${index * 100}ms`,
              animationFillMode: 'both'
            }}
          >
            <ProductCard product={product} />
          </div>
        ))}
      </div>

      {/* Loading skeleton para quando não há produtos */}
      {products.length === 0 && (
        <div className={cn(
          'grid gap-8 md:gap-10 lg:gap-12',
          gridCols[columns || 3],
          'px-4 md:px-0'
        )}>
          {Array.from({ length: 6 }).map((_, index) => (
            <ProductCardSkeleton key={index} />
          ))}
        </div>
      )}
    </div>
  );
}

// Skeleton component premium para loading states
function ProductCardSkeleton() {
  return (
    <div className="flex flex-col overflow-hidden bg-white dark:bg-gray-950 animate-pulse">
      {/* Image skeleton */}
      <div className="aspect-[4/5] bg-gradient-to-br from-gray-200 via-gray-100 to-gray-200 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800">
        <div className="h-full w-full bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-gray-600/20 animate-shimmer" />
      </div>

      {/* Content skeleton */}
      <div className="p-6 space-y-3">
        {/* Brand */}
        <div className="h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded" />

        {/* Title */}
        <div className="space-y-2">
          <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded" />
          <div className="h-4 w-3/4 bg-gray-200 dark:bg-gray-700 rounded" />
        </div>

        {/* Description */}
        <div className="space-y-1">
          <div className="h-3 w-full bg-gray-200 dark:bg-gray-700 rounded" />
          <div className="h-3 w-5/6 bg-gray-200 dark:bg-gray-700 rounded" />
        </div>

        {/* Rating */}
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-3 w-3 bg-gray-200 dark:bg-gray-700 rounded-full" />
            ))}
          </div>
          <div className="h-3 w-8 bg-gray-200 dark:bg-gray-700 rounded" />
        </div>

        {/* Price and button */}
        <div className="flex items-end justify-between pt-2">
          <div className="space-y-1">
            <div className="h-3 w-12 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="h-5 w-20 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
          <div className="h-11 w-11 bg-gray-200 dark:bg-gray-700 rounded-full" />
        </div>
      </div>
    </div>
  );
}

// Layout variations para diferentes contextos
export function PremiumProductGrid({ products, className }: ProductGridProps) {
  return (
    <ProductGrid
      products={products}
      columns={3}
      className={cn(
        // Espaçamento extra premium
        'gap-12 lg:gap-16',
        // Container com max-width para não esticar demais
        'max-w-7xl mx-auto',
        className
      )}
    />
  );
}

export function CompactPremiumGrid({ products, className }: ProductGridProps) {
  return (
    <ProductGrid
      products={products}
      columns={4}
      className={cn(
        'gap-6 lg:gap-8',
        className
      )}
    />
  );
}

// Grid masonry para produtos com alturas diferentes
export function MasonryProductGrid({ products, className }: ProductGridProps) {
  return (
    <div className={cn(
      'columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-8 space-y-8',
      'px-4 md:px-0',
      className
    )}>
      {products.map((product, index) => (
        <div
          key={product.id}
          className="break-inside-avoid animate-in fade-in slide-in-from-bottom-4"
          style={{
            animationDelay: `${index * 100}ms`,
            animationFillMode: 'both'
          }}
        >
          <ProductCard product={product} className="mb-8" />
        </div>
      ))}
    </div>
  );
}
