import { ProductGrid } from "@/modules/product";
import { productService } from "../../modules/product/services/product.service";

export default function ProductsPage() {
	const products = productService.getProducts();

	return (
		<main className="container mx-auto px-4 py-8">
			<h1 className="mb-8 text-3xl font-bold">Nossos Produtos</h1>

			<div className="mb-12">
				<h2 className="mb-6 text-2xl font-semibold">Produtos em Destaque</h2>
				<ProductGrid products={productService.getFeaturedProducts()} columns={4} />
			</div>

			<div className="mb-12">
				<h2 className="mb-6 text-2xl font-semibold">Novidades</h2>
				<ProductGrid products={productService.getNewProducts()} columns={3} />
			</div>

			<div>
				<h2 className="mb-6 text-2xl font-semibold">Todos os Produtos</h2>
				<ProductGrid products={products} columns={4} />
			</div>
		</main>
	);
}
