"use client";

import Link from "next/link";
import { useEffect, useState } from "react";

export interface IBanner {
	id: string;
	title: string;
	subtitle: string;
	description: string;
	image: string;
	mobileImage: string;
	ctaText: string;
	ctaLink: string;
	backgroundColor: string;
	textColor: string;
	position: number;
	active: boolean;
	startDate: string;
	endDate: string;
}

interface IBannerCarouselProps {
	banners: IBanner[];
	autoPlay?: boolean;
	interval?: number;
}

export function BannerCarousel({ banners, autoPlay = true, interval = 5000 }: IBannerCarouselProps) {
	const [currentIndex, setCurrentIndex] = useState(0);

	useEffect(() => {
		if (!autoPlay || banners.length <= 1) return;

		const timer = setInterval(() => {
			setCurrentIndex(prevIndex => (prevIndex === banners.length - 1 ? 0 : prevIndex + 1));
		}, interval);

		return () => clearInterval(timer);
	}, [autoPlay, interval, banners.length]);

	const goToSlide = (index: number) => {
		setCurrentIndex(index);
	};

	const goToPrevious = () => {
		setCurrentIndex(currentIndex === 0 ? banners.length - 1 : currentIndex - 1);
	};

	const goToNext = () => {
		setCurrentIndex(currentIndex === banners.length - 1 ? 0 : currentIndex + 1);
	};

	if (!banners.length) return null;

	return (
		<div className="relative w-full h-64 md:h-96 overflow-hidden rounded-lg">
			{/* Banner slides */}
			<div className="flex transition-transform duration-500 ease-in-out h-full" style={{ transform: `translateX(-${currentIndex * 100}%)` }}>
				{banners.map(banner => (
					<div key={banner.id} className="w-full h-full flex-shrink-0 relative" style={{ backgroundColor: banner.backgroundColor }}>
						{/* Background image */}
						<div className="absolute inset-0 bg-cover bg-center md:hidden" style={{ backgroundImage: `url(${banner.mobileImage})` }} />
						<div className="hidden md:block absolute inset-0 bg-cover bg-center" style={{ backgroundImage: `url(${banner.image})` }} />

						{/* Content overlay */}
						<div className="relative h-full flex items-center">
							<div className="container mx-auto px-4">
								<div className="max-w-lg">
									<h2 className="text-2xl md:text-4xl font-bold mb-2" style={{ color: banner.textColor }}>
										{banner.title}
									</h2>
									<p className="text-lg md:text-xl mb-4" style={{ color: banner.textColor }}>
										{banner.subtitle}
									</p>
									<p className="text-sm md:text-base mb-6 opacity-90" style={{ color: banner.textColor }}>
										{banner.description}
									</p>
									<Link
										href={banner.ctaLink}
										className="inline-block bg-white text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
									>
										{banner.ctaText}
									</Link>
								</div>
							</div>
						</div>
					</div>
				))}
			</div>

			{banners.length > 1 && (
				<>
					<button
						onClick={goToPrevious}
						className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
					>
						<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
						</svg>
					</button>
					<button
						onClick={goToNext}
						className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-75 transition-all"
					>
						<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
						</svg>
					</button>
				</>
			)}

			{/* Dots indicator */}
			{banners.length > 1 && (
				<div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
					{banners.map((_, index) => (
						<button
							key={index}
							onClick={() => goToSlide(index)}
							className={`w-3 h-3 rounded-full transition-all ${index === currentIndex ? "bg-white" : "bg-white bg-opacity-50 hover:bg-opacity-75"}`}
						/>
					))}
				</div>
			)}
		</div>
	);
}

interface ISingleBannerProps {
	banner: IBanner;
}

export function SingleBanner({ banner }: ISingleBannerProps) {
	return (
		<div className="relative w-full h-64 md:h-96 overflow-hidden rounded-lg" style={{ backgroundColor: banner.backgroundColor }}>
			{/* Background image */}
			<div className="absolute inset-0 bg-cover bg-center md:hidden" style={{ backgroundImage: `url(${banner.mobileImage})` }} />
			<div className="hidden md:block absolute inset-0 bg-cover bg-center" style={{ backgroundImage: `url(${banner.image})` }} />

			{/* Content overlay */}
			<div className="relative h-full flex items-center">
				<div className="container mx-auto px-4">
					<div className="max-w-lg">
						<h2 className="text-2xl md:text-4xl font-bold mb-2" style={{ color: banner.textColor }}>
							{banner.title}
						</h2>
						<p className="text-lg md:text-xl mb-4" style={{ color: banner.textColor }}>
							{banner.subtitle}
						</p>
						<p className="text-sm md:text-base mb-6 opacity-90" style={{ color: banner.textColor }}>
							{banner.description}
						</p>
						<Link href={banner.ctaLink} className="inline-block bg-white text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
							{banner.ctaText}
						</Link>
					</div>
				</div>
			</div>
		</div>
	);
}
