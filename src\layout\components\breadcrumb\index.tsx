import { House } from "lucide-react";
import Link from "next/link";

export interface IBreadcrumb {
	label: string;
	href?: string;
}

interface IBreadcrumbProps {
	items: IBreadcrumb[];
	className?: string;
}

export const Breadcrumb: React.FC<IBreadcrumbProps> = ({ items, className }) => {
	return (
		<nav className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`}>
			<Link href="/" className="hover:text-blue-600 transition-colors">
				<House /> Home
			</Link>
			{items.map((item, index) => (
				<div key={index} className="flex items-center space-x-2">
					<span className="text-gray-400">/</span>
					{item.href ? (
						<Link href={item.href} className="hover:text-blue-600 transition-colors">
							{item.label}
						</Link>
					) : (
						<span className="text-gray-900 font-medium">{item.label}</span>
					)}
				</div>
			))}
		</nav>
	);
};
