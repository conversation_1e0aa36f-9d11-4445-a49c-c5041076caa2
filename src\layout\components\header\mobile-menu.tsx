"use client";

import { Bell, Heart, LogOut, Menu, Search, Settings, ShoppingCart, User, X } from "lucide-react";
import Link from "next/link";
import { Avatar, AvatarFallback, AvatarImage } from "../../../shared/components/shadcn/avatar";
import { Badge } from "../../../shared/components/shadcn/badge";
import { Button } from "../../../shared/components/shadcn/button";
import { Input } from "../../../shared/components/shadcn/input";

interface MobileMenuProps {
	isOpen: boolean;
	onToggle: () => void;
	search: string;
	onSearchChange: (value: string) => void;
	user: {
		name: string;
		email: string;
		avatar: string;
		isVip: boolean;
	};
}

export const MobileMenu = ({ isOpen, onToggle, search, onSearchChange, user }: MobileMenuProps) => {
	return (
		<>
			{/* Botão do menu mobile */}
			<Button
				variant="ghost"
				className="md:hidden p-2 hover:bg-slate-100/80 rounded-xl transition-all duration-300"
				onClick={onToggle}
				aria-label="Menu"
			>
				{isOpen ? <X className="h-5 w-5 text-slate-600" /> : <Menu className="h-5 w-5 text-slate-600" />}
			</Button>

			{/* Overlay */}
			{isOpen && <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden" onClick={onToggle} />}

			{/* Menu mobile */}
			<div
				className={`
				fixed top-0 right-0 h-full w-80 bg-white/95 backdrop-blur-md 
				shadow-2xl z-50 transform transition-transform duration-300 ease-out
				${isOpen ? "translate-x-0" : "translate-x-full"}
				md:hidden border-l border-slate-200/50
			`}
			>
				<div className="flex flex-col h-full">
					{/* Header do menu */}
					<div className="flex items-center justify-between p-6 border-b border-slate-100">
						<h2 className="text-lg font-semibold text-slate-800">Menu</h2>
						<Button variant="ghost" className="p-2 hover:bg-slate-100 rounded-lg" onClick={onToggle}>
							<X className="h-5 w-5 text-slate-600" />
						</Button>
					</div>

					{/* Perfil do usuário no mobile */}
					<div className="p-6 border-b border-slate-100">
						<div className="flex items-center space-x-4">
							<div className="relative">
								<Avatar className="h-12 w-12 ring-2 ring-amber-200/50">
									<AvatarImage src={user.avatar} alt={user.name} />
									<AvatarFallback className="bg-gradient-to-br from-slate-800 to-blue-900 text-white font-semibold">
										{user.name
											.split(" ")
											.map(n => n[0])
											.join("")}
									</AvatarFallback>
								</Avatar>
								{user.isVip && (
									<div className="absolute -bottom-1 -right-1 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full p-1">
										<div className="w-2 h-2 bg-white rounded-full"></div>
									</div>
								)}
							</div>
							<div>
								<div className="font-medium text-slate-900">{user.name}</div>
								<div className="text-sm text-slate-500">{user.email}</div>
								{user.isVip && (
									<div className="flex items-center mt-1">
										<div className="px-2 py-0.5 bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-700 text-xs font-medium rounded-full">
											✨ VIP Member
										</div>
									</div>
								)}
							</div>
						</div>
					</div>

					{/* Barra de pesquisa mobile */}
					<div className="p-6 border-b border-slate-100">
						<div className="relative">
							<Input
								type="text"
								placeholder="Buscar produtos..."
								value={search}
								onChange={e => onSearchChange(e.target.value)}
								className="pl-10 pr-4 h-12 border-slate-200 focus:border-amber-300 focus:ring-amber-200/50 bg-white rounded-xl"
							/>
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
						</div>
					</div>

					{/* Ações rápidas */}
					<div className="p-6 border-b border-slate-100">
						<div className="grid grid-cols-3 gap-4">
							<Link
								href="/cart"
								className="flex flex-col items-center p-4 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<div className="relative">
									<ShoppingCart className="h-6 w-6 text-slate-600 mb-2" />
									<Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 bg-gradient-to-r from-slate-800 to-slate-900 text-white text-xs">
										2
									</Badge>
								</div>
								<span className="text-xs text-slate-600 font-medium">Carrinho</span>
							</Link>

							<Link
								href="/favorites"
								className="flex flex-col items-center p-4 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<div className="relative">
									<Heart className="h-6 w-6 text-slate-600 mb-2" />
									<Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs">
										3
									</Badge>
								</div>
								<span className="text-xs text-slate-600 font-medium">Favoritos</span>
							</Link>

							<Link
								href="/notifications"
								className="flex flex-col items-center p-4 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<div className="relative">
									<Bell className="h-6 w-6 text-slate-600 mb-2" />
									<Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs">
										2
									</Badge>
								</div>
								<span className="text-xs text-slate-600 font-medium">Avisos</span>
							</Link>
						</div>
					</div>

					{/* Menu de navegação */}
					<div className="flex-1 p-6">
						<nav className="space-y-2">
							<Link
								href="/profile"
								className="flex items-center space-x-3 p-3 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<User className="h-5 w-5 text-slate-500" />
								<span className="text-slate-700 font-medium">Meu Perfil</span>
							</Link>

							<Link
								href="/orders"
								className="flex items-center space-x-3 p-3 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<ShoppingCart className="h-5 w-5 text-slate-500" />
								<span className="text-slate-700 font-medium">Meus Pedidos</span>
							</Link>

							<Link
								href="/wishlist"
								className="flex items-center space-x-3 p-3 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<Heart className="h-5 w-5 text-slate-500" />
								<span className="text-slate-700 font-medium">Lista de Desejos</span>
							</Link>

							{user.isVip && (
								<Link
									href="/vip"
									className="flex items-center space-x-3 p-3 rounded-xl hover:bg-amber-50 transition-colors duration-200"
									onClick={onToggle}
								>
									<div className="w-5 h-5 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full flex items-center justify-center">
										<div className="w-2 h-2 bg-white rounded-full"></div>
									</div>
									<span className="text-amber-700 font-medium">Benefícios VIP</span>
								</Link>
							)}

							<Link
								href="/settings"
								className="flex items-center space-x-3 p-3 rounded-xl hover:bg-slate-50 transition-colors duration-200"
								onClick={onToggle}
							>
								<Settings className="h-5 w-5 text-slate-500" />
								<span className="text-slate-700 font-medium">Configurações</span>
							</Link>
						</nav>
					</div>

					{/* Footer do menu */}
					<div className="p-6 border-t border-slate-100">
						<button className="flex items-center space-x-3 w-full p-3 rounded-xl hover:bg-red-50 transition-colors duration-200 text-red-600">
							<LogOut className="h-5 w-5" />
							<span className="font-medium">Sair da Conta</span>
						</button>
					</div>
				</div>
			</div>
		</>
	);
};
