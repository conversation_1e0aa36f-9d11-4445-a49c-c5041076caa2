import { Book, Building, Dumbbell, Home, LifeBuoy, Locate, MonitorSmartphone, Shirt, Sparkles, Users } from "lucide-react";

export interface INavItem {
	label: string;
	href: string;
	icon: React.ElementType;
	children?: INavItem[];
}

export const ITEMS_NAVIGATION: INavItem[] = [
	{ label: "Home", href: "/", icon: Home },
	{
		label: "Categorias",
		href: "/categorias",
		icon: Home,
		children: [
			{ label: "Eletrônicos", href: "/categoria/eletronicos", icon: MonitorSmartphone },
			{ label: "Roupas", href: "/categoria/roupas", icon: Shirt },
			{ label: "Esportes", href: "/categoria/esportes", icon: Dumbbell },
			{ label: "Casa", href: "/categoria/casa", icon: Building },
			{ label: "Livros", href: "/categoria/livros", icon: Book },
			{ label: "Belez<PERSON>", href: "/categoria/beleza", icon: Sparkles },
		],
	},
	{ label: "Quem Somos", href: "/quem-somos", icon: Users },
	{ label: "Suporte", href: "/suporte", icon: LifeBuoy },
	{ label: "Rastreamento", href: "/rastreamento", icon: Locate },
];
