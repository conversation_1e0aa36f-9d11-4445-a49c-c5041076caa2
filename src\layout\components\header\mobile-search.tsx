"use client";

import { Search, X } from "lucide-react";
import { useState } from "react";
import { Button } from "../../../shared/components/shadcn/button";
import { Input } from "../../../shared/components/shadcn/input";

interface MobileSearchProps {
	isOpen: boolean;
	onToggle: () => void;
	search: string;
	onSearchChange: (value: string) => void;
}

export const MobileSearch = ({ isOpen, onToggle, search, onSearchChange }: MobileSearchProps) => {
	const [localSearch, setLocalSearch] = useState(search);

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSearchChange(localSearch);
		onToggle();
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 z-50 bg-white md:hidden">
			{/* Header da pesquisa */}
			<div className="flex items-center justify-between p-4 border-b border-slate-200">
				<h2 className="text-lg font-semibold text-slate-800">Buscar Produtos</h2>
				<Button variant="ghost" className="p-2 hover:bg-slate-100 rounded-lg" onClick={onToggle}>
					<X className="h-5 w-5 text-slate-600" />
				</Button>
			</div>

			{/* Formulário de pesquisa */}
			<div className="p-4">
				<form onSubmit={handleSubmit} className="relative">
					<Input
						type="text"
						placeholder="Buscar produtos..."
						value={localSearch}
						onChange={e => setLocalSearch(e.target.value)}
						className="pl-10 pr-4 h-12 border border-slate-200 focus:border-slate-300 focus:ring-1 focus:ring-slate-200 bg-white rounded-lg"
						autoFocus
					/>
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
				</form>
			</div>

			{/* Sugestões populares */}
			<div className="p-4 border-t border-slate-100">
				<div className="text-sm font-medium text-slate-700 mb-3">Sugestões</div>
				<div className="space-y-1">
					{["iPhone 15 Pro", "MacBook Air M3", "AirPods Pro", "Apple Watch", "iPad Pro"].map((suggestion, index) => (
						<button
							key={index}
							className="w-full text-left p-3 rounded-lg hover:bg-slate-50 text-slate-700 transition-colors duration-150 flex items-center"
							onClick={() => {
								setLocalSearch(suggestion);
								onSearchChange(suggestion);
								onToggle();
							}}
						>
							<Search className="h-4 w-4 mr-3 text-slate-400" />
							{suggestion}
						</button>
					))}
				</div>
			</div>

			{/* Categorias rápidas */}
			<div className="p-4 border-t border-slate-100">
				<div className="text-sm font-medium text-slate-700 mb-3">Categorias</div>
				<div className="grid grid-cols-2 gap-2">
					{["Eletrônicos", "Roupas", "Casa", "Esportes", "Livros", "Beleza"].map((category, index) => (
						<button
							key={index}
							className="p-3 text-center rounded-lg bg-slate-50 hover:bg-slate-100 text-slate-700 transition-colors duration-150"
							onClick={() => {
								setLocalSearch(category);
								onSearchChange(category);
								onToggle();
							}}
						>
							{category}
						</button>
					))}
				</div>
			</div>
		</div>
	);
};
