"use client";
import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { useState } from "react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../../../shared/components/shadcn/dropdown-menu";
import { ITEMS_NAVIGATION } from "../../constants/header/navigation-items.constant";

export const Navigation = () => {
	const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

	return (
		<nav className="bg-white border-b border-slate-100/60 shadow-sm">
			{/* Gradient accent line */}
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-amber-200/20 to-transparent"></div>

			<div className="container mx-auto px-4 md:px-6">
				<div className="flex items-center justify-start">
					{/* Desktop Navigation */}
					<div className="hidden md:flex items-center space-x-8 py-4">
						{ITEMS_NAVIGATION.map((item, index) => {
							const Icon = item.icon;

							if (item.children && item.children.length > 0) {
								// Dropdown menu for items with children
								return (
									<DropdownMenu key={index}>
										<DropdownMenuTrigger asChild>
											<button
												className="group flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-slate-50/80 transition-all duration-300 ease-out"
												onMouseEnter={() => setActiveDropdown(item.label)}
												onMouseLeave={() => setActiveDropdown(null)}
											>
												<Icon className="h-4 w-4 text-slate-500 group-hover:text-amber-600 transition-colors duration-300" />
												<span className="text-slate-700 group-hover:text-slate-900 font-medium transition-colors duration-300">
													{item.label}
												</span>
												<ChevronDown className="h-3 w-3 text-slate-400 group-hover:text-amber-600 transition-all duration-300 group-hover:rotate-180" />

												{/* Elegant underline */}
												<span className="absolute bottom-0 left-1/2 w-0 h-px bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 group-hover:w-full group-hover:left-0 transition-all duration-500 ease-out"></span>
											</button>
										</DropdownMenuTrigger>

										<DropdownMenuContent
											className="w-56 mt-2 bg-white/95 backdrop-blur-md border border-slate-200/50 shadow-xl rounded-xl"
											align="start"
										>
											{item.children.map((child, childIndex) => {
												const ChildIcon = child.icon;
												return (
													<DropdownMenuItem key={childIndex} asChild>
														<Link
															href={child.href}
															className="flex items-center space-x-3 px-4 py-3 cursor-pointer hover:bg-slate-50 transition-colors duration-200 rounded-lg mx-1"
														>
															<ChildIcon className="h-4 w-4 text-slate-500" />
															<span className="text-slate-700">{child.label}</span>
														</Link>
													</DropdownMenuItem>
												);
											})}
										</DropdownMenuContent>
									</DropdownMenu>
								);
							} else {
								// Regular navigation link
								return (
									<Link
										key={index}
										href={item.href}
										className="group relative flex items-center space-x-2 px-4 py-2 rounded-lg hover:bg-slate-50/80 transition-all duration-300 ease-out"
									>
										<Icon className="h-4 w-4 text-slate-500 group-hover:text-amber-600 transition-colors duration-300" />
										<span className="text-slate-700 group-hover:text-slate-900 font-medium transition-colors duration-300">
											{item.label}
										</span>

										{/* Elegant underline */}
										<span className="absolute bottom-0 left-1/2 w-0 h-px bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 group-hover:w-full group-hover:left-0 transition-all duration-500 ease-out"></span>
									</Link>
								);
							}
						})}
					</div>

					{/* Mobile Navigation - Simplified horizontal scroll */}
					<div className="md:hidden w-full overflow-x-auto py-3">
						<div className="flex items-center space-x-6 px-2 min-w-max">
							{ITEMS_NAVIGATION.map((item, index) => {
								const Icon = item.icon;
								return (
									<Link
										key={index}
										href={item.href}
										className="group flex flex-col items-center space-y-1 px-3 py-2 rounded-lg hover:bg-slate-50/80 transition-all duration-300 ease-out min-w-max"
									>
										<Icon className="h-4 w-4 text-slate-500 group-hover:text-amber-600 transition-colors duration-300" />
										<span className="text-xs text-slate-600 group-hover:text-slate-800 font-medium transition-colors duration-300">
											{item.label}
										</span>
									</Link>
								);
							})}
						</div>
					</div>
				</div>
			</div>
		</nav>
	);
};
