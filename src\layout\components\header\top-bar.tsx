import Link from "next/link";

const TOP_BAR_INFO = {
	message: "Experiência premium em cada detalhe",
	links: [
		{ href: "/rastreamento", label: "Rastrear Pedido" },
		{ href: "/suporte", label: "Atendimento" },
	],
};

export const TopBar = () => {
	return (
		<div className="relative bg-gradient-to-r from-slate-900 via-blue-950 to-slate-800 text-white border-b border-slate-700/30 backdrop-blur-sm">
			{/* Linha de destaque superior dourada para combinar com logo */}
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-amber-400/50 to-transparent"></div>

			{/* Efeito de textura mais sutil e refinada */}
			<div
				className="absolute inset-0 opacity-3"
				style={{
					backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)`,
					backgroundSize: "24px 24px",
				}}
			></div>

			{/* Brilho sutil de fundo */}
			<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/[0.02] to-transparent"></div>

			<div className="relative container mx-auto px-6 py-3.5 flex justify-between items-center">
				{/* Mensagem premium minimalista */}
				<div className="flex items-center space-x-4">
					{/* Ícone premium minimalista */}
					<div className="flex items-center space-x-3">
						<div className="relative">
							{/* Ponto dourado elegante */}
							<div className="w-1.5 h-1.5 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-full"></div>
							<div className="absolute inset-0 w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse opacity-50"></div>
						</div>
						<span className="text-xs font-light text-slate-400 uppercase tracking-[0.2em]">PREMIUM</span>
					</div>

					{/* Separador elegante */}
					<div className="h-4 w-px bg-gradient-to-b from-transparent via-slate-600/50 to-transparent"></div>

					<span className="text-sm font-light text-slate-100 tracking-wide">{TOP_BAR_INFO.message}</span>
				</div>

				{/* Links de navegação premium harmonizados */}
				<div className="hidden md:flex items-center space-x-8">
					{TOP_BAR_INFO.links.map((link, index) => (
						<Link
							key={index}
							href={link.href}
							className="group relative flex items-center space-x-2 text-sm font-medium text-slate-200 hover:text-white transition-all duration-300 ease-out"
						>
							{/* Ícone minimalista com estilo refinado */}
							<div className="w-3.5 h-3.5 flex items-center justify-center text-slate-400 group-hover:text-amber-300 transition-colors duration-300">
								{link.href.includes("rastreamento") ? (
									<svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m6.75 4.5v-3a1.5 1.5 0 011.5-1.5h3a1.5 1.5 0 011.5 1.5v3m-6.75 0H21a.75.75 0 00.75-.75V6.108c0-1.036-.84-1.875-1.875-1.875H16.5a2.25 2.25 0 00-2.25 2.25v8.617c0 1.035.84 1.875 1.875 1.875z"
										/>
									</svg>
								) : (
									<svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z"
										/>
									</svg>
								)}
							</div>

							<span className="relative font-light" style={{ fontFamily: "Inter, system-ui, sans-serif" }}>
								{link.label}
								{/* Underline dourado elegante igual ao logo */}
								<span className="absolute -bottom-1 left-0 w-0 h-px bg-gradient-to-r from-amber-400 via-yellow-500 to-amber-400 group-hover:w-full transition-all duration-500 ease-out"></span>
							</span>
						</Link>
					))}
				</div>

				{/* Menu mobile premium */}
				<div className="md:hidden">
					<button className="group relative p-2.5 text-slate-200 hover:text-white hover:bg-slate-800/40 rounded-lg transition-all duration-300 border border-transparent hover:border-slate-700/50">
						{/* Efeito de brilho no hover */}
						<div className="absolute inset-0 rounded-lg bg-gradient-to-r from-amber-500/5 to-yellow-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
						<svg className="relative w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={1.5}>
							<path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
						</svg>
					</button>
				</div>
			</div>

			{/* Linha de destaque inferior com toque dourado */}
			<div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-slate-600/40 to-transparent"></div>

			{/* Pequenos pontos de destaque dourados nas bordas */}
			<div className="absolute bottom-0 left-8 w-1 h-px bg-amber-400/30"></div>
			<div className="absolute bottom-0 right-8 w-1 h-px bg-amber-400/30"></div>
		</div>
	);
};
