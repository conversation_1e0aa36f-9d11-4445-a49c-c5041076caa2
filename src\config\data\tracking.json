{"BR123456789": {"trackingCode": "BR123456789", "orderId": "ORD-2024-001", "status": "delivered", "estimatedDelivery": "2024-01-15", "actualDelivery": "2024-01-14", "recipient": "<PERSON>", "address": "<PERSON><PERSON>, 123 - São Paulo, SP", "events": [{"date": "2024-01-10", "time": "14:30", "status": "order_confirmed", "description": "Pedido confirmado e pagamento aprovado", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-11", "time": "09:15", "status": "preparing", "description": "Produto separado e embalado", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-11", "time": "16:45", "status": "shipped", "description": "Produto despachado para entrega", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-12", "time": "08:20", "status": "in_transit", "description": "Produto em trânsito", "location": "Centro de Distribuição Regional - Campinas, SP"}, {"date": "2024-01-13", "time": "10:30", "status": "out_for_delivery", "description": "Produto saiu para entrega", "location": "Agência dos Correios - São Paulo, SP"}, {"date": "2024-01-14", "time": "15:22", "status": "delivered", "description": "Produto entregue ao destinatário", "location": "<PERSON><PERSON>, 123 - São Paulo, SP"}]}, "BR987654321": {"trackingCode": "BR987654321", "orderId": "ORD-2024-002", "status": "in_transit", "estimatedDelivery": "2024-01-20", "actualDelivery": null, "recipient": "<PERSON>", "address": "<PERSON>v<PERSON>, 456 - São Paulo, SP", "events": [{"date": "2024-01-15", "time": "11:20", "status": "order_confirmed", "description": "Pedido confirmado e pagamento aprovado", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-16", "time": "14:10", "status": "preparing", "description": "Produto separado e embalado", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-17", "time": "09:30", "status": "shipped", "description": "Produto despachado para entrega", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-18", "time": "13:45", "status": "in_transit", "description": "Produto em trânsito", "location": "Centro de Distribuição Regional - Rio de Janeiro, RJ"}]}, "BR555666777": {"trackingCode": "BR555666777", "orderId": "ORD-2024-003", "status": "preparing", "estimatedDelivery": "2024-01-25", "actualDelivery": null, "recipient": "<PERSON>", "address": "<PERSON><PERSON> Comércio, 789 - <PERSON><PERSON> Horizon<PERSON>, MG", "events": [{"date": "2024-01-18", "time": "16:00", "status": "order_confirmed", "description": "Pedido confirmado e pagamento aprovado", "location": "Centro de Distribuição - São Paulo, SP"}, {"date": "2024-01-19", "time": "10:30", "status": "preparing", "description": "Produto sendo separado e embalado", "location": "Centro de Distribuição - São Paulo, SP"}]}}