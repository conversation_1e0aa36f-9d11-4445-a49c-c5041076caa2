"use client";
import { Badge } from "@/shared/components/shadcn/badge";
import { cn } from "@/shared/lib/shadcn";
import Image from "next/image";
import Link from "next/link";
import { ProductCardProps } from "../types";

import { Eye, Heart, ImageOff, ShoppingBag } from "lucide-react";
import { useState } from "react";

export function ProductCard({ product, className }: ProductCardProps) {
	const [imageError, setImageError] = useState(false);
	const [isWishlisted, setIsWishlisted] = useState(false);
	const [currentImageIndex, setCurrentImageIndex] = useState(0);

	return (
		<div
			className={cn(
				"group relative flex flex-col overflow-hidden bg-white transition-all duration-500 ease-out hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/10 dark:bg-gray-950 dark:hover:shadow-black/20",
				// Removi border e rounded para look mais premium
				className
			)}
		>
			{/* Premium badges com glassmorphism */}
			<div className="absolute left-4 top-4 z-20 flex flex-col gap-2">
				{product.isNew && (
					<Badge className="backdrop-blur-md bg-white/20 border border-white/30 text-white shadow-lg">
						Novo
					</Badge>
				)}
				{product.discount && product.discount > 0 && (
					<Badge className="backdrop-blur-md bg-red-500/90 border border-red-400/30 text-white shadow-lg">
						-{product.discount}%
					</Badge>
				)}
			</div>

			{/* Actions overlay - aparece só no hover */}
			<div className="absolute right-4 top-4 z-20 flex flex-col gap-2 opacity-0 translate-x-4 transition-all duration-300 group-hover:opacity-100 group-hover:translate-x-0">
				<button
					onClick={(e) => {
						e.preventDefault();
						setIsWishlisted(!isWishlisted);
					}}
					className="flex h-10 w-10 items-center justify-center rounded-full backdrop-blur-md bg-white/20 border border-white/30 text-white transition-all duration-200 hover:bg-white hover:text-gray-900 hover:scale-110"
					aria-label="Adicionar aos favoritos"
				>
					<Heart className={cn("h-4 w-4 transition-all", isWishlisted && "fill-current text-red-500")} />
				</button>

				<button
					className="flex h-10 w-10 items-center justify-center rounded-full backdrop-blur-md bg-white/20 border border-white/30 text-white transition-all duration-200 hover:bg-white hover:text-gray-900 hover:scale-110"
					aria-label="Visualização rápida"
				>
					<Eye className="h-4 w-4" />
				</button>
			</div>

			{/* Image container com aspect ratio premium */}
			<Link
				href={`/produtos/${product.slug}`}
				className="relative aspect-[4/5] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800"
				onMouseEnter={() => {
					// Muda para segunda imagem no hover se existir
					if (product.images && product.images.length > 1) {
						setCurrentImageIndex(1);
					}
				}}
				onMouseLeave={() => {
					setCurrentImageIndex(0);
				}}
			>
				{product.images && Array.isArray(product.images) && product.images[currentImageIndex] && !imageError ? (
					<>
						<Image
							src={product.images[currentImageIndex]}
							alt={product.name}
							fill
							className="object-cover transition-all duration-700 ease-out group-hover:scale-110"
							onError={() => setImageError(true)}
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						/>
						{/* Overlay gradient sutil */}
						<div className="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
					</>
				) : (
					<div className="flex items-center h-full justify-center p-8 text-gray-300">
						<ImageOff className="h-16 w-16" />
					</div>
				)}

				{/* Indicador de múltiplas imagens */}
				{product.images && product.images.length > 1 && (
					<div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-1 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
						{product.images.slice(0, 3).map((_, index) => (
							<div
								key={index}
								className={cn(
									"h-1.5 w-1.5 rounded-full transition-all",
									index === currentImageIndex
										? "bg-white w-4"
										: "bg-white/50"
								)}
							/>
						))}
					</div>
				)}
			</Link>

			{/* Content com mais espaçamento premium */}
			<div className="flex flex-1 flex-col p-6 space-y-3">
				{/* Brand com tipografia refinada */}
				<div className="text-xs font-medium tracking-wide text-gray-500 uppercase dark:text-gray-400">
					{product.brand}
				</div>

				{/* Product name com melhor hierarquia */}
				<Link
					href={`/produtos/${product.slug}`}
					className="font-semibold text-gray-900 dark:text-white leading-tight hover:text-primary transition-colors duration-200 line-clamp-2"
				>
					{product.name}
				</Link>

				{/* Description mais sutil */}
				<p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2 leading-relaxed">
					{product.shortDescription}
				</p>

				{/* Rating mais elegante */}
				<div className="flex items-center gap-2 text-sm">
					<div className="flex items-center">
						{Array.from({ length: 5 }).map((_, i) => (
							<svg
								key={i}
								className={cn(
									"h-3.5 w-3.5 transition-colors",
									i < Math.floor(product.rating)
										? "text-amber-400 fill-current"
										: "text-gray-200 dark:text-gray-600"
								)}
								viewBox="0 0 20 20"
								fill="currentColor"
							>
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
							</svg>
						))}
					</div>
					<span className="text-gray-500 dark:text-gray-400 text-xs">
						({product.reviewCount})
					</span>
				</div>

				{/* Pricing section premium */}
				<div className="flex items-end justify-between pt-2">
					<div className="space-y-1">
						{product.originalPrice && product.originalPrice > product.price && (
							<div className="text-sm text-gray-400 line-through">
								R$ {product.originalPrice.toFixed(2)}
							</div>
						)}
						<div className="text-xl font-bold text-gray-900 dark:text-white">
							R$ {product.price.toFixed(2)}
						</div>
						{product.originalPrice && product.originalPrice > product.price && (
							<div className="text-xs font-medium text-green-600 dark:text-green-400">
								Economize R$ {(product.originalPrice - product.price).toFixed(2)}
							</div>
						)}
					</div>

					{/* CTA Button mais sofisticado */}
					<button
						className="group/btn relative flex h-11 w-11 items-center justify-center rounded-full bg-primary text-white transition-all duration-300 hover:bg-primary/90 hover:scale-110 hover:shadow-lg hover:shadow-primary/25 active:scale-95"
						aria-label="Adicionar ao carrinho"
					>
						<ShoppingBag className="h-4 w-4 transition-transform group-hover/btn:scale-110" />

						{/* Ripple effect */}
						<div className="absolute inset-0 rounded-full bg-white/20 scale-0 transition-transform duration-300 group-hover/btn:scale-100 group-hover/btn:animate-pulse" />
					</button>
				</div>
			</div>

			{/* Hover overlay para toda a carta */}
			<div className="absolute inset-0 border border-transparent transition-all duration-300 group-hover:border-primary/20 group-hover:shadow-primary/5" />
		</div>
	);
}
