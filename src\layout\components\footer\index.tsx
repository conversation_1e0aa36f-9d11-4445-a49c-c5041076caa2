import { Mail, MapPin, MessageCircle, Phone } from "lucide-react";
import Link from "next/link";

const FOOTER_DETAILS = {
	company: {
		name: "Kevin & Co.",
		description: "Experiência premium em cada detalhe. Sua loja online de confiança com os melhores produtos e atendimento excepcional.",
		tagline: "Premium E-Commerce",
		socialLinks: [
			{ href: "#", label: "Facebook" },
			{ href: "#", label: "Instagram" },
			{ href: "#", label: "Twitter" },
			{ href: "#", label: "LinkedIn" },
		],
	},
	contact: {
		email: "<EMAIL>",
		phone: "(11) 1234-5678",
		chat: "Chat online 24h",
		address: "Rua Exemplo, 123 - São Paulo, SP",
	},
};

export default function Footer() {
	const currentYear = new Date().getFullYear();

	return (
		<footer className="relative bg-gradient-to-br from-slate-900 via-blue-950 to-slate-800 text-white">
			{/* Top gradient accent line */}
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-amber-400/40 to-transparent"></div>

			{/* Subtle texture overlay */}
			<div
				className="absolute inset-0 opacity-5"
				style={{
					backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)`,
					backgroundSize: "32px 32px",
				}}
			></div>

			{/* Main footer content */}
			<div className="relative">
				{/* Premium brand section */}
				<div className="border-b border-slate-700/30">
					<div className="container mx-auto px-4 md:px-6 py-12 md:py-16">
						<div className="text-center space-y-6">
							{/* Logo and brand */}
							<Link href="/" className="group inline-flex items-center space-x-4 md:space-x-6">
								{/* Enhanced Logo */}
								<div className="relative">
									<div className="absolute inset-0 w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-br from-amber-50/10 via-yellow-25/10 to-amber-100/10 shadow-2xl transform group-hover:scale-105 transition-all duration-500"></div>

									<div className="relative flex items-center justify-center w-16 h-16 md:w-20 md:h-20 rounded-full bg-gradient-to-br from-slate-800 via-blue-900 to-slate-700 shadow-2xl border border-slate-600/30 group-hover:shadow-3xl transition-all duration-500">
										<div className="absolute inset-1 rounded-full bg-gradient-to-br from-white/10 to-transparent"></div>

										<span
											className="relative z-10 font-serif font-bold text-2xl md:text-3xl text-white tracking-tighter drop-shadow-lg group-hover:scale-110 transition-transform duration-500"
											style={{
												fontFamily: "Playfair Display, Georgia, Times, serif",
												letterSpacing: "-0.05em",
												textShadow: "0 2px 4px rgba(0,0,0,0.4)",
											}}
										>
											K
										</span>

										<span
											className="absolute bottom-1 right-1.5 text-sm font-light text-white/95 group-hover:text-white transition-colors duration-500"
											style={{
												fontFamily: "Inter, system-ui, sans-serif",
												letterSpacing: "0.03em",
												fontSize: "0.7rem",
											}}
										>
											&Co.
										</span>

										<div className="absolute bottom-1.5 left-1/2 w-4 md:w-5 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent transform -translate-x-1/2 group-hover:via-white/60 transition-colors duration-500"></div>
									</div>

									<div className="absolute top-3 right-3 w-1.5 h-1.5 rounded-full bg-white/60 blur-sm group-hover:bg-white/80 transition-colors duration-500"></div>
								</div>

								{/* Brand text */}
								<div className="space-y-2">
									<h2 className="text-3xl md:text-4xl font-light tracking-tight group-hover:scale-105 transition-transform duration-500">
										<span
											className="font-serif bg-gradient-to-r from-white via-slate-100 to-amber-100 bg-clip-text text-transparent group-hover:from-amber-100 group-hover:to-white transition-all duration-500"
											style={{
												fontFamily: "Playfair Display, Georgia, Times, serif",
												fontWeight: "400",
											}}
										>
											{FOOTER_DETAILS.company.name}
										</span>
									</h2>

									<div className="flex items-center justify-center space-x-3">
										<div className="w-8 md:w-12 h-px bg-gradient-to-r from-amber-400 to-yellow-500 group-hover:from-amber-300 group-hover:to-yellow-400 transition-colors duration-500"></div>
										<p
											className="text-sm md:text-base text-slate-300 font-medium uppercase tracking-widest group-hover:text-slate-200 transition-colors duration-500"
											style={{
												fontFamily: "Inter, system-ui, sans-serif",
												letterSpacing: "0.15em",
											}}
										>
											{FOOTER_DETAILS.company.tagline}
										</p>
										<div className="w-8 md:w-12 h-px bg-gradient-to-r from-yellow-500 to-amber-400 group-hover:from-yellow-400 group-hover:to-amber-300 transition-colors duration-500"></div>
									</div>
								</div>
							</Link>

							{/* Description */}
							<p
								className="text-slate-300 text-lg md:text-xl font-light max-w-2xl mx-auto leading-relaxed"
								style={{ fontFamily: "Inter, system-ui, sans-serif" }}
							>
								{FOOTER_DETAILS.company.description}
							</p>
						</div>
					</div>
				</div>

				{/* Links and information section */}
				<div className="border-b border-slate-700/30">
					<div className="container mx-auto px-4 md:px-6 py-12 md:py-16">
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12">
							{/* Quick Links */}
							<div className="space-y-6">
								<h4
									className="text-xl font-light text-white tracking-tight"
									style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
								>
									Links Rápidos
								</h4>
								<div className="w-12 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
								<ul className="space-y-4">
									{[
										{ href: "/", label: "Home" },
										{ href: "/categorias", label: "Categorias" },
										{ href: "/quem-somos", label: "Quem Somos" },
										{ href: "/suporte", label: "Suporte" },
										{ href: "/rastreamento", label: "Rastreamento" },
									].map((link, index) => (
										<li key={index}>
											<Link
												href={link.href}
												className="group flex items-center space-x-2 text-slate-300 hover:text-white transition-all duration-300 ease-out"
												style={{ fontFamily: "Inter, system-ui, sans-serif" }}
											>
												<span className="w-1 h-1 bg-slate-500 rounded-full group-hover:bg-amber-400 transition-colors duration-300"></span>
												<span className="group-hover:translate-x-1 transition-transform duration-300">{link.label}</span>
											</Link>
										</li>
									))}
								</ul>
							</div>

							{/* Categories */}
							<div className="space-y-6">
								<h4
									className="text-xl font-light text-white tracking-tight"
									style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
								>
									Categorias
								</h4>
								<div className="w-12 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
								<ul className="space-y-4">
									{[
										{ href: "/categoria/eletronicos", label: "Eletrônicos" },
										{ href: "/categoria/roupas", label: "Roupas" },
										{ href: "/categoria/esportes", label: "Esportes" },
										{ href: "/categoria/casa", label: "Casa e Decoração" },
										{ href: "/categoria/livros", label: "Livros" },
										{ href: "/categoria/beleza", label: "Beleza" },
									].map((link, index) => (
										<li key={index}>
											<Link
												href={link.href}
												className="group flex items-center space-x-2 text-slate-300 hover:text-white transition-all duration-300 ease-out"
												style={{ fontFamily: "Inter, system-ui, sans-serif" }}
											>
												<span className="w-1 h-1 bg-slate-500 rounded-full group-hover:bg-amber-400 transition-colors duration-300"></span>
												<span className="group-hover:translate-x-1 transition-transform duration-300">{link.label}</span>
											</Link>
										</li>
									))}
								</ul>
							</div>

							{/* Contact Information */}
							<div className="space-y-6">
								<h4
									className="text-xl font-light text-white tracking-tight"
									style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
								>
									Contato
								</h4>
								<div className="w-12 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
								<div className="space-y-4">
									<div className="group flex items-center space-x-3 text-slate-300 hover:text-white transition-all duration-300">
										<div className="flex items-center justify-center w-8 h-8 rounded-lg bg-slate-800/50 group-hover:bg-amber-500/20 transition-colors duration-300">
											<Mail className="h-4 w-4 group-hover:text-amber-400 transition-colors duration-300" />
										</div>
										<span style={{ fontFamily: "Inter, system-ui, sans-serif" }}>{FOOTER_DETAILS.contact.email}</span>
									</div>
									<div className="group flex items-center space-x-3 text-slate-300 hover:text-white transition-all duration-300">
										<div className="flex items-center justify-center w-8 h-8 rounded-lg bg-slate-800/50 group-hover:bg-amber-500/20 transition-colors duration-300">
											<Phone className="h-4 w-4 group-hover:text-amber-400 transition-colors duration-300" />
										</div>
										<span style={{ fontFamily: "Inter, system-ui, sans-serif" }}>{FOOTER_DETAILS.contact.phone}</span>
									</div>
									<div className="group flex items-center space-x-3 text-slate-300 hover:text-white transition-all duration-300">
										<div className="flex items-center justify-center w-8 h-8 rounded-lg bg-slate-800/50 group-hover:bg-amber-500/20 transition-colors duration-300">
											<MessageCircle className="h-4 w-4 group-hover:text-amber-400 transition-colors duration-300" />
										</div>
										<span style={{ fontFamily: "Inter, system-ui, sans-serif" }}>{FOOTER_DETAILS.contact.chat}</span>
									</div>
									<div className="group flex items-start space-x-3 text-slate-300 hover:text-white transition-all duration-300">
										<div className="flex items-center justify-center w-8 h-8 rounded-lg bg-slate-800/50 group-hover:bg-amber-500/20 transition-colors duration-300 mt-0.5">
											<MapPin className="h-4 w-4 group-hover:text-amber-400 transition-colors duration-300" />
										</div>
										<span style={{ fontFamily: "Inter, system-ui, sans-serif" }}>{FOOTER_DETAILS.contact.address}</span>
									</div>
								</div>
							</div>

							{/* Social Links */}
							<div className="space-y-6">
								<h4
									className="text-xl font-light text-white tracking-tight"
									style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
								>
									Redes Sociais
								</h4>
								<div className="w-12 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
								<div className="flex flex-wrap gap-4">
									{FOOTER_DETAILS.company.socialLinks.map((social, index) => (
										<Link
											key={index}
											href={social.href}
											className="group flex items-center justify-center w-12 h-12 rounded-xl bg-slate-800/50 hover:bg-gradient-to-br hover:from-amber-500/20 hover:to-yellow-500/20 border border-slate-700/50 hover:border-amber-500/30 transition-all duration-300 ease-out"
										>
											<span className="text-slate-400 group-hover:text-amber-400 transition-colors duration-300 text-sm font-medium">
												{social.label.charAt(0)}
											</span>
										</Link>
									))}
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Payment and Security Section */}
				<div className="border-t border-slate-700/30">
					<div className="container mx-auto px-4 md:px-6 py-8 md:py-12">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
							{/* Payment Methods */}
							<div className="space-y-4">
								<h5
									className="text-lg font-light text-white tracking-tight"
									style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
								>
									Formas de Pagamento
								</h5>
								<div className="w-8 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
								<div className="flex flex-wrap gap-3">
									{["Cartão", "PIX", "Boleto", "Débito"].map((method, index) => (
										<div
											key={index}
											className="flex items-center justify-center px-4 py-2 rounded-lg bg-slate-800/50 border border-slate-700/50 hover:border-amber-500/30 transition-colors duration-300"
										>
											<span
												className="text-slate-300 text-sm font-medium"
												style={{ fontFamily: "Inter, system-ui, sans-serif" }}
											>
												{method}
											</span>
										</div>
									))}
								</div>
							</div>

							{/* Security */}
							<div className="space-y-4">
								<h5
									className="text-lg font-light text-white tracking-tight"
									style={{ fontFamily: "Playfair Display, Georgia, Times, serif" }}
								>
									Segurança
								</h5>
								<div className="w-8 h-px bg-gradient-to-r from-amber-400 to-yellow-500"></div>
								<div className="flex flex-wrap gap-3">
									{["SSL", "Criptografia", "Verificado"].map((security, index) => (
										<div
											key={index}
											className="flex items-center justify-center px-4 py-2 rounded-lg bg-slate-800/50 border border-slate-700/50 hover:border-green-500/30 transition-colors duration-300"
										>
											<span
												className="text-slate-300 text-sm font-medium"
												style={{ fontFamily: "Inter, system-ui, sans-serif" }}
											>
												{security}
											</span>
										</div>
									))}
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Copyright Section */}
				<div className="border-t border-slate-700/30">
					<div className="container mx-auto px-4 md:px-6 py-6 md:py-8">
						<div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
							<p className="text-slate-400 text-sm" style={{ fontFamily: "Inter, system-ui, sans-serif" }}>
								© {currentYear} {FOOTER_DETAILS.company.name}. Todos os direitos reservados.
							</p>
							<div className="flex flex-wrap gap-6">
								{[
									{ href: "/termos", label: "Termos de Uso" },
									{ href: "/privacidade", label: "Política de Privacidade" },
									{ href: "/cookies", label: "Cookies" },
								].map((link, index) => (
									<Link
										key={index}
										href={link.href}
										className="text-slate-400 hover:text-white text-sm transition-colors duration-300"
										style={{ fontFamily: "Inter, system-ui, sans-serif" }}
									>
										{link.label}
									</Link>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Bottom accent line */}
			<div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-amber-400/30 to-transparent"></div>
		</footer>
	);
}
