import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { GlobalProvider } from "../config/providers/global.provider";
import { MainContainer } from "../layout/components/container/main-container";
import "../layout/styles/theme.css";

const inter = Inter({
	variable: "--font-inter",
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: "E-Commerce - Sua loja online de confiança",
	description: "Encontre os melhores produtos com os melhores preços. Eletrônicos, roupas, esportes, livros e muito mais.",
	keywords: "e-commerce, loja online, produtos, eletrônicos, roupas, esportes, livros",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body className={`${inter.variable} font-sans antialiased`}>
				<MainContainer>
					<GlobalProvider>{children}</GlobalProvider>
				</MainContainer>
			</body>
		</html>
	);
}
